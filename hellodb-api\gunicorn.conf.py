import os
import logging


class HealthCheckFilter(logging.Filter):
    """过滤健康检查请求的日志过滤器"""
    def filter(self, record):
        # 过滤掉 /health 请求的日志
        if hasattr(record, 'getMessage'):
            message = record.getMessage()
            if '/health' in message and ('GET' in message or 'POST' in message):
                return False
        return True


def when_ready(server):
    """服务器启动后的回调函数，用于设置日志过滤器"""
    # 获取访问日志记录器
    access_logger = logging.getLogger("gunicorn.access")
    # 添加健康检查过滤器
    access_logger.addFilter(HealthCheckFilter())


# Gunicorn 配置
bind = f"{os.getenv('APP_BIND_ADDRESS', '0.0.0.0')}:{os.getenv('PORT', '5001')}"
workers = int(os.getenv('SERVER_WORKER_AMOUNT', '1'))
worker_class = os.getenv('SERVER_WORKER_CLASS', 'gevent')
timeout = int(os.getenv('GUNICORN_TIMEOUT', '200'))

# 日志文件配置
accesslog = '/app/logs/access.log'
errorlog = '/app/logs/error.log'
loglevel = 'warning'

# 其他配置
preload_app = True
capture_output = True
