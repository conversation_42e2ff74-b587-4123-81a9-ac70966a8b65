from celery import Celery, Task
from flask import Flask
from config import Config
from celery.schedules import crontab
import os
import logging
from logging.handlers import RotatingFileHandler


# class FlaskTask(Task):
#     """Custom Celery task to ensure Flask app context."""
#     _app = None
    
#     @classmethod
#     def set_app(cls, app):
#         cls._app = app
    
#     def __call__(self, *args, **kwargs):
#         with self._app.app_context():
#             return self.run(*args, **kwargs)
        
def init_app(app: Flask) -> Celery:
    # 设置应用实例到 FlaskTask 类
    # FlaskTask.set_app(app)

    class FlaskTask(Task):
        def __call__(self, *args: object, **kwargs: object) -> object:
            with app.app_context():
                return self.run(*args, **kwargs)
    
    config = Config()
    
    broker_transport_options = {}
    
    # 支持 Redis Sentinel（高可用 Redis 集群）
    if config.CELERY_USE_SENTINEL:
        broker_transport_options = {
            "master_name": config.CELERY_SENTINEL_MASTER_NAME,
            "sentinel_kwargs": {
                "socket_timeout": config.CELERY_SENTINEL_SOCKET_TIMEOUT, 
            },
        }
    
    # 配置 Celery 日志
    log_dir = os.path.dirname(config.LOG_FILE)
    os.makedirs(log_dir, exist_ok=True)
    
    # Worker 日志配置
    worker_log_file = os.path.join(log_dir, 'celery_worker.log')
    worker_handler = RotatingFileHandler(
        filename=worker_log_file,
        maxBytes=1024 * 1024 * 1024,  # 1GB
        backupCount=5
    )
    worker_handler.setFormatter(logging.Formatter(
        fmt=config.LOG_FORMAT,
        datefmt=config.LOG_DATEFORMAT
    ))
    
    # Beat 日志配置
    beat_log_file = os.path.join(log_dir, 'celery_beat.log')
    beat_handler = RotatingFileHandler(
        filename=beat_log_file,
        maxBytes=1024 * 1024 * 1024,  # 1GB
        backupCount=5
    )
    beat_handler.setFormatter(logging.Formatter(
        fmt=config.LOG_FORMAT,
        datefmt=config.LOG_DATEFORMAT
    ))
    
    # 确保 /app/data 目录存在
    data_dir = '/app/data'
    os.makedirs(data_dir, exist_ok=True)
    
    celery_app = Celery(
        app.name,
        task_cls=FlaskTask,
        broker=config.CELERY_BROKER_URL,
        backend=config.CELERY_BACKEND,
        task_ignore_result=True,
        include=['tasks.datasource_statistics_task']
    )

    celery_app.conf.update(
        result_backend=config.CELERY_RESULT_BACKEND,   # 结果后端 URL（与 backend 一致）。
        broker_transport_options=broker_transport_options,  # 应用 Sentinel 配置。
        broker_connection_retry_on_startup=True,            # 启动时自动重试连接消息代理，增强健壮性。
        task_serializer='json',
        accept_content=['json'],
        result_serializer='json',
        timezone='Asia/Shanghai',
        enable_utc=True,
        task_track_started=True,
        task_time_limit=3600,  # 1小时超时
        worker_max_tasks_per_child=200,  # 每个worker处理200个任务后重启
        worker_prefetch_multiplier=1,  # 每次只预取一个任务
        task_acks_late=True,  # 任务执行完成后再确认
        task_reject_on_worker_lost=True,  # worker异常退出时拒绝任务
        task_default_queue='default',
        task_queues={
            'default': {
                'exchange': 'default',
                'routing_key': 'default',
            },
            'chatdb': {
                'exchange': 'chatdb',
                'routing_key': 'chatdb',
            }
        },
        task_default_exchange='default',
        task_default_exchange_type='direct',
        task_default_routing_key='default',
        # beat_schedule={
        #     'update-all-apps-statistics': {
        #         'task': 'tasks.datasource_statistics_task.update_all_apps_statistics',
        #         'schedule': crontab(minute=0),  # 每小时执行一次
        #         'options': {'queue': 'chatdb'}
        #     }
        # },
        # 日志配置
        worker_log_format=config.LOG_FORMAT,
        worker_log_datefmt=config.LOG_DATEFORMAT,
        worker_log_file=worker_log_file,
        worker_log_level=config.LOG_LEVEL,
        beat_log_format=config.LOG_FORMAT,
        beat_log_datefmt=config.LOG_DATEFORMAT,
        beat_log_file=beat_log_file,
        beat_log_level=config.LOG_LEVEL,
        # Beat 调度文件配置
        # beat_schedule_filename=os.path.join(data_dir, 'celerybeat-schedule')
    )

    # Add SSL options to the Celery configuration
    ssl_options = {
        "ssl_cert_reqs": None,
        "ssl_ca_certs": None,
        "ssl_certfile": None,
        "ssl_keyfile": None,
    }

    if config.BROKER_USE_SSL:
        celery_app.conf.update(
            broker_use_ssl=ssl_options,    # Add the SSL options to the broker configuration
        )

    celery_app.set_default()               # 将当前 Celery 实例设置为全局默认，方便任务导入。
    app.extensions["celery"] = celery_app  # 将 Celery 实例存储在 Flask 应用的扩展中，便于其他模块访问。

    # imports=['tasks.chatdb_train_database_task.train_vanna_task'],  # 确保导入任务模块

    # celery_app.conf.update(
    #     imports=imports
    # )

    return celery_app
