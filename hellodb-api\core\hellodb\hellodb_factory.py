from typing import Op<PERSON>

from openai import OpenAI


from config import Config
from models.datasource import DataSourceApp

from core.hellodb.hellodb import Hellodb
from core.hellodb.database.mysql_hellodb import MySQLHellodb
from core.hellodb.database.postgresql_hellodb import PostgreSQLHellodb
from core.hellodb.database.sqlserver_hellodb import SQLServerHellodb
from core.hellodb.database.clickhouse_hellodb import ClickHouseHellodb
from core.hellodb.database.oracle_hellodb import OracleHellodb


class HellodbFactory:
    """
    Factory class for creating HellodbVanna instances based on database type.
    """

    @staticmethod
    def create_hellodb(datasourceApp: DataSourceApp) -> Hellodb:
        """
        Create and return a HellodbVanna instance based on the datasource type.

        Args:
            datasource (Datasource): The datasource object containing connection information

        Returns:
            Hellodb: An instance of the appropriate Hellodb implementation

        Raises:
            ValueError: If the database type is not supported
        """

        config = Config()
        VANNA_LLM_MODEL = config.VANNA_LLM_MODEL
        VANNA_LLM_API_KEY = config.VANNA_LLM_API_KEY
        VANNA_LLM_BASE_URL = config.VANNA_LLM_BASE_URL

        if datasourceApp is None:
            path = '/data/hellodb/chroma/default'
            initial_prompt = ''
        else:
            path = f'/data/hellodb/chroma/{datasourceApp.alias}'
            initial_prompt = datasourceApp.initial_prompt

        hellodb_config = {
            'path' : path,
            'model': VANNA_LLM_MODEL,
            'api_key': VANNA_LLM_API_KEY,
            'initial_prompt': initial_prompt,
            'language': '中文'
        }

        client = OpenAI(api_key=VANNA_LLM_API_KEY, base_url=VANNA_LLM_BASE_URL)

        db_type = datasourceApp.datasource_type.lower() if datasourceApp.datasource_type else ""

        if db_type == "mysql":
            return MySQLHellodb(client=client, config=hellodb_config)
        elif db_type == "postgresql":
            return PostgreSQLHellodb(client=client, config=hellodb_config)
        elif db_type == "sqlserver":
            return SQLServerHellodb(client=client, config=hellodb_config)
        elif db_type == "clickhouse":
            return ClickHouseHellodb(client=client, config=hellodb_config)
        # elif db_type == "hive":
        #     return HiveHellodbVanna(client=client, config=config)
        elif db_type == "oracle":
            return OracleHellodb(client=client, config=hellodb_config)
        else:
            raise ValueError(f"Unsupported database type: {datasourceApp.datasource_type}")

    @staticmethod
    def create_and_connect(datasourceApp: DataSourceApp, client=None, config=None) -> Hellodb:
        """
        Create a HellodbVanna instance and connect to the database.

        Args:
            datasourceApp (DataSourceApp): The datasource app object containing connection information
            client: The client to use for the HellodbVanna instance
            config: The configuration to use for the HellodbVanna instance

        Returns:
            HellodbVanna: A connected instance of the appropriate HellodbVanna implementation

        Raises:
            ValueError: If the database type is not supported
        """
        vanna = HellodbFactory.create_hellodb(datasourceApp, client=client, config=config)
        vanna.connect_to_database(datasourceApp.datasource)
        return vanna